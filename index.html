<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pointeuse d'Activité Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'gray-900': '#121212',
              'gray-800': '#1e1e1e',
              'gray-700': '#2d2d2d',
              'gray-600': '#4a4a4a',
              'gray-400': '#9b9b9b',
              'gray-200': '#e0e0e0',
              'teal-500': '#14b8a6',
              'teal-600': '#0d9488',
            }
          }
        }
      }
    </script>

  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "firebase/": "https://esm.sh/firebase@^12.0.0/"
  }
}
</script>
</head>
  <body class="bg-gray-900 text-gray-200">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>